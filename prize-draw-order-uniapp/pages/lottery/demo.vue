<template>
  <view class="lottery-container">
    <!-- 商家信息 -->
    <view class="merchant-header">
      <view class="merchant-name">测试商家</view>
      <view class="activity-name">九宫格抽奖活动</view>
    </view>

    <!-- 九宫格抽奖 -->
    <view class="lottery-grid-container">
      <view class="grid-wrapper">
        <view class="lottery-grid">
          <view 
            class="grid-item" 
            v-for="(item, index) in gridItems" 
            :key="index"
            :class="{ 'active': currentIndex === index, 'center': index === 4 }"
            @click="index === 4 ? startLottery() : null"
          >
            <view v-if="index === 4" class="center-button">
              <view class="center-text">{{ isDrawing ? '抽奖中...' : '点击抽奖' }}</view>
              <view class="remaining-text">剩余{{ remainingDraws }}次</view>
            </view>
            <view v-else class="prize-item">
              <view class="prize-icon">🎁</view>
              <view class="prize-name">{{ item.prizeName }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 奖品列表 -->
    <view class="prize-list">
      <view class="prize-title">奖品设置</view>
      <view class="prize-items">
        <view class="prize-item" v-for="(prize, index) in prizeList" :key="index">
          <view class="prize-name">{{ prize.prizeName }}</view>
          <view class="prize-probability">中奖率：{{ prize.probability }}%</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      remainingDraws: 3,
      isDrawing: false,
      currentIndex: -1,
      animationTimer: null,
      animationSpeed: 100,
      animationCount: 0,
      targetIndex: -1,
      prizeList: [
        { prizeName: '一等奖', probability: 5 },
        { prizeName: '二等奖', probability: 10 },
        { prizeName: '三等奖', probability: 15 },
        { prizeName: '四等奖', probability: 20 },
        { prizeName: '五等奖', probability: 25 },
        { prizeName: '六等奖', probability: 15 },
        { prizeName: '谢谢参与', probability: 10 }
      ],
      gridItems: []
    }
  },

  onLoad() {
    this.initGrid()
  },

  methods: {
    initGrid() {
      // 创建九宫格数据，确保有8个奖品位置（中间是抽奖按钮）
      this.gridItems = []
      
      // 如果奖品不足8个，用"谢谢参与"填充
      const prizes = [...this.prizeList]
      while (prizes.length < 8) {
        prizes.push({
          prizeName: '谢谢参与',
          prizeType: 'thanks',
          probability: 0
        })
      }
      
      // 如果奖品超过8个，只取前8个
      if (prizes.length > 8) {
        prizes.splice(8)
      }
      
      // 按照九宫格顺序排列
      const gridOrder = [0, 1, 2, 3, 4, 5, 6, 7]
      gridOrder.forEach(i => {
        if (i < prizes.length) {
          this.gridItems.push(prizes[i])
        } else {
          this.gridItems.push({
            prizeName: '谢谢参与',
            prizeType: 'thanks',
            probability: 0
          })
        }
      })
    },

    startLottery() {
      if (this.isDrawing) return
      if (this.remainingDraws <= 0) {
        uni.showToast({
          title: '抽奖次数已用完',
          icon: 'none'
        })
        return
      }

      this.isDrawing = true
      this.remainingDraws--

      // 模拟抽奖结果
      const randomIndex = Math.floor(Math.random() * 8)
      
      // 开始九宫格动画
      this.startGridAnimation(randomIndex, () => {
        this.showResult(this.gridItems[randomIndex])
        this.isDrawing = false
      })
    },

    startGridAnimation(targetIndex, callback) {
      this.targetIndex = targetIndex
      this.animationCount = 0
      this.currentIndex = 0
      this.animationSpeed = 100
      
      // 动画总圈数和最终位置
      const totalRounds = 3 // 转3圈
      const totalSteps = totalRounds * 8 + targetIndex
      
      const animate = () => {
        this.currentIndex = this.animationCount % 8
        this.animationCount++
        
        // 动态调整速度，最后几步减速
        if (this.animationCount > totalSteps - 10) {
          this.animationSpeed = 200
        } else if (this.animationCount > totalSteps - 20) {
          this.animationSpeed = 150
        }
        
        if (this.animationCount >= totalSteps) {
          // 动画结束
          this.currentIndex = targetIndex
          clearTimeout(this.animationTimer)
          setTimeout(callback, 500) // 延迟500ms显示结果
        } else {
          // 继续动画
          this.animationTimer = setTimeout(animate, this.animationSpeed)
        }
      }
      
      animate()
    },

    showResult(result) {
      const isWinner = result.prizeName !== '谢谢参与'
      const title = isWinner ? '恭喜中奖！' : '很遗憾'
      const content = isWinner ? `您获得了：${result.prizeName}` : '谢谢参与，再接再厉！'

      uni.showModal({
        title: title,
        content: content,
        showCancel: false,
        confirmText: '确定'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lottery-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
}

.merchant-header {
  text-align: center;
  margin-bottom: 60rpx;

  .merchant-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10rpx;
  }

  .activity-name {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.lottery-grid-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .grid-wrapper {
    width: 600rpx;
    height: 600rpx;
  }

  .lottery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 8rpx;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .grid-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.active {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      transform: scale(1.05);
      box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
      
      .prize-item {
        .prize-icon {
          animation: bounce 0.6s ease-in-out;
        }
        
        .prize-name {
          color: #fff;
          font-weight: bold;
        }
      }
    }

    &.center {
      background: linear-gradient(135deg, #667eea, #764ba2);
      cursor: pointer;
      
      &:hover {
        transform: scale(1.02);
      }
      
      &:active {
        transform: scale(0.98);
      }
    }
  }

  .prize-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 10rpx;

    .prize-icon {
      font-size: 48rpx;
      margin-bottom: 8rpx;
    }

    .prize-name {
      font-size: 24rpx;
      color: #333;
      line-height: 1.2;
      word-break: break-all;
    }
  }

  .center-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #fff;

    .center-text {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .remaining-text {
      font-size: 20rpx;
      opacity: 0.9;
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

.prize-list {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;

  .prize-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .prize-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .prize-item {
    flex: 1;
    min-width: 200rpx;
    background: #f8f9fa;
    border-radius: 10rpx;
    padding: 20rpx;
    text-align: center;

    .prize-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .prize-probability {
      font-size: 24rpx;
      color: #666;
    }
  }
}
</style>
